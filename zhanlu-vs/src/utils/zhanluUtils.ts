import * as vscode from "vscode"
import { ClineProvider } from "../core/webview/ClineProvider"
import { ProviderSettings } from "@roo-code/types"

export const ZHANLU_PROFILES = [
	{
		name: "deepseek-v3",
		modelId: "deepseek-v3",
	},
	{
		name: "deepseek-r1-0528",
		modelId: "deepseek-r1-0528",
	},
	{
		name: "zhanlu-r1",
		modelId: "zhanlu-r1",
	},
	{
		name: "zhanlu",
		modelId: "zhanluAI",
	},
	{
		name: "jiutian-75b",
		modelId: "jiutian-75b",
	},
] as const

/**
 * Get all Zhanlu profile names
 */
export const getZhanluProfileNames = (): string[] => {
	return ZHANLU_PROFILES.map((profile) => profile.name)
}
/**
 * Clear Zhanlu credentials from global state and provider context
 * @param context VS Code extension context
 * @param provider The Cline provider instance
 */
export const clearZhanluGlobalCredentials = async (
	context: vscode.ExtensionContext,
	provider?: ClineProvider,
): Promise<void> => {
	// Clear from VS Code global state (legacy insecure storage)
	await context.globalState.update("zhanluAccessKey", undefined)
	await context.globalState.update("zhanluSecretKey", undefined)
	await context.globalState.update("zhanluToken", undefined)
	await context.globalState.update("zhanluUserName", undefined)
	await context.globalState.update("zhanluEmail", undefined)

	// Clear from encrypted Secrets storage (current secure storage)
	await context.secrets.delete("zhanluAccessKey")
	await context.secrets.delete("zhanluSecretKey")
	await context.secrets.delete("zhanluToken")
	await context.secrets.delete("zhanluUserName")
	await context.secrets.delete("zhanluEmail")

	// Also clear from ContextProxy cache if available
	if (provider) {
		await provider.setValue("zhanluAccessKey", undefined)
		await provider.setValue("zhanluSecretKey", undefined)
		await provider.setValue("zhanluToken", undefined)
		// Clear user info secrets using the new method
		await provider.contextProxy.deleteArbitrarySecret("zhanluUserName")
		await provider.contextProxy.deleteArbitrarySecret("zhanluEmail")
	}
}

/**
 * Clear Zhanlu profiles created during login
 * @param provider The Cline provider instance
 * @param profileNames Array of profile names to delete
 * @param outputChannel VS Code output channel for logging
 */
export const clearZhanluProfiles = async (
	provider: ClineProvider,
	profileNames: string[],
	outputChannel: vscode.OutputChannel,
): Promise<void> => {
	outputChannel.appendLine(`Deleting Zhanlu profiles: ${profileNames.join(", ")}`)
	// create default profile to undefined
	await provider.upsertProviderProfile("default", { apiProvider: undefined as any }, true)

	// Delete all specified profiles regardless of their provider settings
	for (const profileName of profileNames) {
		try {
			await provider.providerSettingsManager.deleteConfig(profileName)
			outputChannel.appendLine(`Deleted profile: ${profileName}`)
		} catch (error) {
			// Profile might not exist, continue with next one
			outputChannel.appendLine(`Profile ${profileName} not found or already deleted: ${error}`)
		}
	}
}

/**
 * Complete Zhanlu logout process
 * @param context VS Code extension context
 * @param provider The Cline provider instance
 * @param profileNames Array of Zhanlu profile names to clear
 * @param outputChannel VS Code output channel for logging
 */
export const performZhanluLogout = async (
	context: vscode.ExtensionContext,
	provider: ClineProvider,
	profileNames: string[],
	outputChannel: vscode.OutputChannel,
): Promise<void> => {
	try {
		outputChannel.appendLine("Starting comprehensive Zhanlu logout process...")

		// 1. Clear global credentials first
		await clearZhanluGlobalCredentials(context, provider)
		outputChannel.appendLine("✓ Global credentials cleared")

		// 2. Clear all zhanlu profiles (including default if it's using zhanlu)
		await clearZhanluProfiles(provider, profileNames, outputChannel)
		outputChannel.appendLine("✓ Zhanlu profiles cleared")

		// 3. Ensure current API configuration is properly set to anthropic
		const currentState = await provider.getState()
		const currentConfigName = currentState.currentApiConfigName || "default"

		// Create a clean undefined configuration (without id field as it's handled internally)
		const undefinedConfig: ProviderSettings = {
			apiProvider: undefined as any,
		}

		// Save the undefined configuration for the current profile
		await provider.upsertProviderProfile(currentConfigName, undefinedConfig, true)
		outputChannel.appendLine(`✓ Current profile '${currentConfigName}' reset to undefined`)

		// 4. Clear VS Code authentication context
		await vscode.commands.executeCommand("setContext", "zhanlu.isAuthenticated", false)
		outputChannel.appendLine("✓ Authentication context cleared")

		// 5. Clear any cached zhanlu credentials from current provider settings
		const currentProviderSettings = provider.contextProxy.getProviderSettings()
		if (
			currentProviderSettings.apiProvider === "zhanlu" ||
			currentProviderSettings.zhanluAccessKey ||
			currentProviderSettings.zhanluSecretKey ||
			currentProviderSettings.zhanluToken
		) {
			// Reset provider settings to clean undefined configuration
			await provider.contextProxy.setProviderSettings({
				apiProvider: undefined as any,
			})
			outputChannel.appendLine("✓ Provider settings reset to undefined")
		}

		// 6. Force refresh state to ensure frontend gets updated state
		await provider.postStateToWebview()
		outputChannel.appendLine("✓ Frontend state refreshed")

		// 7. Small delay to ensure all state updates are processed
		await new Promise((resolve) => setTimeout(resolve, 200))

		// 8. Notify frontend to navigate to login page
		await provider.postMessageToWebview({ type: "navigateToLogin" })
		outputChannel.appendLine("✓ Frontend notified to show login page")

		outputChannel.appendLine("✅ Zhanlu logout process completed successfully")
	} catch (error) {
		outputChannel.appendLine(`❌ Error during logout process: ${error}`)
		// Even if there's an error, try to navigate to login
		try {
			await provider.postMessageToWebview({ type: "navigateToLogin" })
		} catch (navError) {
			outputChannel.appendLine(`❌ Failed to navigate to login: ${navError}`)
		}
		throw error
	}
}
