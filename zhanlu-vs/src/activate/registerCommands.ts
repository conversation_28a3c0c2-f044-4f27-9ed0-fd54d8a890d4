import * as vscode from "vscode"
import delay from "delay"

import type { CommandId } from "@roo-code/types"
import { TelemetryService } from "@roo-code/telemetry"

import { Package } from "../shared/package"
import { getCommand } from "../utils/commands"
import { ClineProvider } from "../core/webview/ClineProvider"
import { applyZhanluMessageHandlers } from "../extension"
import { ContextProxy } from "../core/config/ContextProxy"
import { focusPanel } from "../utils/focusPanel"

import { registerHumanRelayCallback, unregisterHumanRelayCallback, handleHumanRelayResponse } from "./humanRelay"
import { handleNewTask } from "./handleTask"
import { CodeIndexManager } from "../services/code-index/manager"
import { importSettingsWithFeedback } from "../core/config/importExport"
import { MdmService } from "../services/mdm/MdmService"
import { getZhanluProfileNames, perform<PERSON><PERSON>lu<PERSON><PERSON>ut, clearZhanluGlobalCredentials } from "../utils/zhanluUtils"
import { t } from "../i18n"

/**
 * Helper to get the visible ClineProvider instance or log if not found.
 */
export function getVisibleProviderOrLog(outputChannel: vscode.OutputChannel): ClineProvider | undefined {
	const visibleProvider = ClineProvider.getVisibleInstance()
	if (!visibleProvider) {
		outputChannel.appendLine("Cannot find any visible Zhanlu instances.")
		return undefined
	}
	return visibleProvider
}

// Store panel references in both modes
let sidebarPanel: vscode.WebviewView | undefined = undefined
let tabPanel: vscode.WebviewPanel | undefined = undefined

/**
 * Get the currently active panel
 * @returns WebviewPanel或WebviewView
 */
export function getPanel(): vscode.WebviewPanel | vscode.WebviewView | undefined {
	return tabPanel || sidebarPanel
}

/**
 * Set panel references
 */
export function setPanel(
	newPanel: vscode.WebviewPanel | vscode.WebviewView | undefined,
	type: "sidebar" | "tab",
): void {
	if (type === "sidebar") {
		sidebarPanel = newPanel as vscode.WebviewView
		tabPanel = undefined
	} else {
		tabPanel = newPanel as vscode.WebviewPanel
		sidebarPanel = undefined
	}
}

export type RegisterCommandOptions = {
	context: vscode.ExtensionContext
	outputChannel: vscode.OutputChannel
	provider: ClineProvider
}

export const registerCommands = (options: RegisterCommandOptions) => {
	const { context } = options

	for (const [id, callback] of Object.entries(getCommandsMap(options))) {
		const command = getCommand(id as CommandId)
		context.subscriptions.push(vscode.commands.registerCommand(command, callback))
	}
}

const getCommandsMap = ({ context, outputChannel, provider }: RegisterCommandOptions): Record<CommandId, any> => ({
	activationCompleted: () => {},
	plusButtonClicked: async () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("plus")
		// clear all task state to show new task interface
		await visibleProvider.clearAllTaskState()

		await visibleProvider.removeClineFromStack()
		await visibleProvider.postStateToWebview()
		await visibleProvider.postMessageToWebview({ type: "action", action: "chatButtonClicked" })
	},
	mcpButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("mcp")

		visibleProvider.postMessageToWebview({ type: "action", action: "mcpButtonClicked" })
	},
	promptsButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("prompts")

		visibleProvider.postMessageToWebview({ type: "action", action: "promptsButtonClicked" })
	},
	popoutButtonClicked: () => {
		TelemetryService.instance.captureTitleButtonClicked("popout")

		return openClineInNewTab({ context, outputChannel })
	},
	openInNewTab: () => openClineInNewTab({ context, outputChannel }),
	settingsButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("settings")

		visibleProvider.postMessageToWebview({ type: "action", action: "settingsButtonClicked" })
		// Also explicitly post the visibility message to trigger scroll reliably
		visibleProvider.postMessageToWebview({ type: "action", action: "didBecomeVisible" })
	},
	historyButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("history")

		visibleProvider.postMessageToWebview({ type: "action", action: "historyButtonClicked" })
	},
	marketplaceButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)
		if (!visibleProvider) return
		visibleProvider.postMessageToWebview({ type: "action", action: "marketplaceButtonClicked" })
	},
	roomoteAgentButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)
		if (!visibleProvider) return

		TelemetryService.instance.captureTitleButtonClicked("roomoteAgent")

		visibleProvider.postMessageToWebview({ type: "action", action: "roomoteAgentButtonClicked" })
	},
	accountButtonClicked: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		TelemetryService.instance.captureTitleButtonClicked("account")

		visibleProvider.postMessageToWebview({ type: "action", action: "accountButtonClicked" })
	},
	logoutButtonClicked: async () => {
		try {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) return

			// check if already logged out
			const accessKey = visibleProvider.contextProxy.getSecret("zhanluAccessKey")
			const secretKey = visibleProvider.contextProxy.getSecret("zhanluSecretKey")
			const token = visibleProvider.contextProxy.getSecret("zhanluToken")

			// if credentials not exist or empty, means already logged out
			const hasCredentials = !!(
				accessKey &&
				accessKey.trim() !== "" &&
				secretKey &&
				secretKey.trim() !== "" &&
				token &&
				token.trim() !== ""
			)

			if (!hasCredentials) {
				outputChannel.appendLine("Already logged out, no action needed")
				return
			}

			// Get Zhanlu profile names from the centralized utility
			const zhanluProfileNames = getZhanluProfileNames()

			// Perform complete logout process
			await performZhanluLogout(context, visibleProvider, zhanluProfileNames, outputChannel)

			vscode.window.setStatusBarMessage("✅ 湛卢账户已退出登录", 3000)
		} catch (error) {
			outputChannel.appendLine(`Error clearing ZhanLu credentials: ${error}`)
			vscode.window.showErrorMessage(`湛卢退出登录失败: ${error}`)
		}
	},
	showHumanRelayDialog: (params: { requestId: string; promptText: string }) => {
		const panel = getPanel()

		if (panel) {
			panel?.webview.postMessage({
				type: "showHumanRelayDialog",
				requestId: params.requestId,
				promptText: params.promptText,
			})
		}
	},
	registerHumanRelayCallback: registerHumanRelayCallback,
	unregisterHumanRelayCallback: unregisterHumanRelayCallback,
	handleHumanRelayResponse: handleHumanRelayResponse,
	newTask: handleNewTask,
	setCustomStoragePath: async () => {
		const { promptForCustomStoragePath } = await import("../utils/storage")
		await promptForCustomStoragePath()
	},
	importSettings: async (filePath?: string) => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)
		if (!visibleProvider) {
			return
		}

		await importSettingsWithFeedback(
			{
				providerSettingsManager: visibleProvider.providerSettingsManager,
				contextProxy: visibleProvider.contextProxy,
				customModesManager: visibleProvider.customModesManager,
				provider: visibleProvider,
			},
			filePath,
		)
	},
	focusInput: async () => {
		try {
			await focusPanel(tabPanel, sidebarPanel)

			// Send focus input message only for sidebar panels
			if (sidebarPanel && getPanel() === sidebarPanel) {
				provider.postMessageToWebview({ type: "action", action: "focusInput" })
			}
		} catch (error) {
			outputChannel.appendLine(`Error focusing input: ${error}`)
		}
	},
	focusPanel: async () => {
		try {
			await focusPanel(tabPanel, sidebarPanel)
		} catch (error) {
			outputChannel.appendLine(`Error focusing panel: ${error}`)
		}
	},
	acceptInput: () => {
		const visibleProvider = getVisibleProviderOrLog(outputChannel)

		if (!visibleProvider) {
			return
		}

		visibleProvider.postMessageToWebview({ type: "acceptInput" })
	},
	storeAsl: async (aslData: {
		AccessKey: string
		secretKey: string
		token: string
		userName?: string
		email?: string
	}) => {
		try {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) {
				outputChannel.appendLine("No visible provider available for storing credentials")
				return
			}

			// Use ContextProxy to store credentials securely in Secrets
			await visibleProvider.contextProxy.setValue("zhanluAccessKey", aslData.AccessKey)
			await visibleProvider.contextProxy.setValue("zhanluSecretKey", aslData.secretKey)
			await visibleProvider.contextProxy.setValue("zhanluToken", aslData.token)

			// Store user info if available
			if (aslData.userName) {
				await context.secrets.store("zhanluUserName", aslData.userName)
			}
			if (aslData.email) {
				await context.secrets.store("zhanluEmail", aslData.email)
			}

			// Set authentication context
			await vscode.commands.executeCommand("setContext", "zhanlu.isAuthenticated", true)

			// notify webview to update login status
			await visibleProvider.postMessageToWebview({
				type: "commandResponse",
				command: "checkZhanluLoginStatusResponse",
				hasCredentials: true,
			})

			outputChannel.appendLine("ZhanLu credentials stored securely in encrypted storage")
			// Inform user the credentials have been stored (auto-dismiss after 3 seconds)
			vscode.window.setStatusBarMessage("✅ 湛卢登录成功", 3000)
		} catch (error) {
			outputChannel.appendLine(`Zhanlu login failed: ${error}`)
			vscode.window.showErrorMessage(`湛卢登录失败: ${error}`)
		}
	},
	clearZhanluCredentials: async () => {
		try {
			const visibleProvider = getVisibleProviderOrLog(outputChannel)
			if (!visibleProvider) {
				outputChannel.appendLine("No visible provider available for clearing credentials")
				return
			}

			// Clear only the global credentials (not profiles)
			await clearZhanluGlobalCredentials(context, visibleProvider)
			outputChannel.appendLine("ZhanLu credentials cleared successfully")

			// Update VS Code authentication context
			await vscode.commands.executeCommand("setContext", "zhanlu.isAuthenticated", false)

			// Force refresh state to ensure frontend gets updated state
			await visibleProvider.postStateToWebview()

			// Send navigate to login message to frontend
			await visibleProvider.postMessageToWebview({ type: "navigateToLogin" })
			outputChannel.appendLine("✓ Frontend notified to show login page")

			// Inform user the credentials have been cleared
			vscode.window.setStatusBarMessage("✅ 湛卢退出登录成功", 3000)
		} catch (error) {
			outputChannel.appendLine(`湛卢退出登录失败: ${error}`)
			vscode.window.showErrorMessage(`湛卢退出登录失败: ${error}`)
		}
	},
})

export const openClineInNewTab = async ({ context, outputChannel }: Omit<RegisterCommandOptions, "provider">) => {
	// (This example uses webviewProvider activation event which is necessary to
	// deserialize cached webview, but since we use retainContextWhenHidden, we
	// don't need to use that event).
	// https://github.com/microsoft/vscode-extension-samples/blob/main/webview-sample/src/extension.ts
	const contextProxy = await ContextProxy.getInstance(context)
	const codeIndexManager = CodeIndexManager.getInstance(context)

	// Get the existing MDM service instance to ensure consistent policy enforcement
	let mdmService: MdmService | undefined
	try {
		mdmService = MdmService.getInstance()
	} catch (error) {
		// MDM service not initialized, which is fine - extension can work without it
		mdmService = undefined
	}

	const tabProvider = new ClineProvider(context, outputChannel, "editor", contextProxy, codeIndexManager, mdmService)
	const lastCol = Math.max(...vscode.window.visibleTextEditors.map((editor) => editor.viewColumn || 0))

	// Check if there are any visible text editors, otherwise open a new group
	// to the right.
	const hasVisibleEditors = vscode.window.visibleTextEditors.length > 0

	if (!hasVisibleEditors) {
		await vscode.commands.executeCommand("workbench.action.newGroupRight")
	}

	const targetCol = hasVisibleEditors ? Math.max(lastCol + 1, 1) : vscode.ViewColumn.Two

	const newPanel = vscode.window.createWebviewPanel(ClineProvider.tabPanelId, "Zhanlu", targetCol, {
		enableScripts: true,
		retainContextWhenHidden: true,
		localResourceRoots: [context.extensionUri],
	})

	// Save as tab type panel.
	setPanel(newPanel, "tab")

	// TODO: Use better svg icon with light and dark variants (see
	// https://stackoverflow.com/questions/58365687/vscode-extension-iconpath).
	newPanel.iconPath = {
		light: vscode.Uri.joinPath(context.extensionUri, "assets", "icons", "icon.png"),
		dark: vscode.Uri.joinPath(context.extensionUri, "assets", "icons", "icon.png"),
	}

	await tabProvider.resolveWebviewView(newPanel)

	// 应用Zhanlu消息处理程序到新创建的webview面板
	applyZhanluMessageHandlers(context, tabProvider, newPanel.webview)

	// Handle panel closing events.
	newPanel.onDidDispose(
		() => {
			setPanel(undefined, "tab")
		},
		null,
		context.subscriptions, // Also register dispose listener
	)

	// Lock the editor group so clicking on files doesn't open them over the panel.
	await delay(100)
	await vscode.commands.executeCommand("workbench.action.lockEditorGroup")

	return tabProvider
}
